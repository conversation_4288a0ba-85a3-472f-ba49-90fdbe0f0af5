"use client"

import { useState, use<PERSON>em<PERSON>, useEffect, useRef, useCallback } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { Slider } from "@/components/ui/slider"
import {
  Search,
  Plus,
  Check,
  Waves,
  Filter,
  X,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Loader2
} from "lucide-react"
import { useGetNatureSounds } from "@schemas/Natural/nature-sound-query"
import { useAddNatureSoundsToMusicPlaylistUser, useGetMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { create } from "zustand"

interface AddNatureSoundsDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  playlistId: string
  playlistName: string
}

// Individual natural sound player state
interface NatureSoundPlayer {
  id: string
  isPlaying: boolean
  volume: number
  isMuted: boolean
  audioElement: HTMLAudioElement | null
}

// Zustand store for managing multiple natural sound players
interface NatureSoundPlayersState {
  players: Record<string, NatureSoundPlayer>
  createPlayer: (id: string, src: string) => void
  togglePlay: (id: string) => void
  setVolume: (id: string, volume: number) => void
  toggleMute: (id: string) => void
  removePlayer: (id: string) => void
  cleanup: () => void
}

const useNatureSoundPlayers = create<NatureSoundPlayersState>((set, get) => ({
  players: {},

  createPlayer: (id: string, src: string) => {
    const { players } = get()
    if (players[id]) return // Player already exists

    const audio = new Audio(src)
    audio.loop = true // Natural sounds should loop
    audio.volume = 0.8 // Default volume

    const player: NatureSoundPlayer = {
      id,
      isPlaying: false,
      volume: 80,
      isMuted: false,
      audioElement: audio
    }

    // Set up event listeners
    audio.addEventListener('play', () => {
      set(state => ({
        players: {
          ...state.players,
          [id]: { ...state.players[id], isPlaying: true }
        }
      }))
    })

    audio.addEventListener('pause', () => {
      set(state => ({
        players: {
          ...state.players,
          [id]: { ...state.players[id], isPlaying: false }
        }
      }))
    })

    set(state => ({
      players: { ...state.players, [id]: player }
    }))
  },

  togglePlay: (id: string) => {
    const { players } = get()
    const player = players[id]
    if (!player?.audioElement) return

    if (player.isPlaying) {
      player.audioElement.pause()
    } else {
      player.audioElement.play().catch(error => {
        console.error(`Failed to play natural sound ${id}:`, error)
      })
    }
  },

  setVolume: (id: string, volume: number) => {
    const { players } = get()
    const player = players[id]
    if (!player?.audioElement) return

    if (volume === 0) {
      // When dragging to 0, mute but keep the previous volume for restoration
      player.audioElement.volume = 0
      set(state => ({
        players: {
          ...state.players,
          [id]: { ...state.players[id], isMuted: true }
        }
      }))
    } else {
      // When dragging above 0, unmute and set new volume
      player.audioElement.volume = volume / 100
      set(state => ({
        players: {
          ...state.players,
          [id]: { ...state.players[id], volume, isMuted: false }
        }
      }))
    }
  },

  toggleMute: (id: string) => {
    const { players } = get()
    const player = players[id]
    if (!player?.audioElement) return

    const newMuted = !player.isMuted
    player.audioElement.volume = newMuted ? 0 : player.volume / 100

    set(state => ({
      players: {
        ...state.players,
        [id]: { ...state.players[id], isMuted: newMuted }
      }
    }))
  },

  removePlayer: (id: string) => {
    const { players } = get()
    const player = players[id]
    if (player?.audioElement) {
      player.audioElement.pause()
      player.audioElement.src = ''
    }

    set(state => {
      const newPlayers = { ...state.players }
      delete newPlayers[id]
      return { players: newPlayers }
    })
  },

  cleanup: () => {
    const { players } = get()
    Object.values(players).forEach(player => {
      if (player.audioElement) {
        player.audioElement.pause()
        player.audioElement.src = ''
      }
    })
    set({ players: {} })
  }
}))

// Map category enum values to display names
const categoryDisplayNames: Record<string, string> = {
  RAIN: "Rain",
  FOREST: "Forest",
  OCEAN: "Ocean",
  THUNDER: "Thunder",
  WIND: "Wind",
  FIRE: "Fire",
  BIRDS: "Birds",
  WATERFALL: "Waterfall",
  STREAM: "Stream",
  WAVES: "Waves",
  NIGHT: "Night",
  INSECTS: "Insects",
  RIVER: "River",
  STORM: "Storm",
  LAKE: "Lake",
  WHITE_NOISE: "White Noise",
  AMBIENT: "Ambient",
  CITY: "City",
  PEOPLE: "People"
}

export function AddNatureSoundsDialog({ isOpen, onOpenChange, playlistId, playlistName }: AddNatureSoundsDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [addedNatureSoundIds, setAddedNatureSoundIds] = useState<Set<string>>(new Set())
  const [hoveredNatureSoundId, setHoveredNatureSoundId] = useState<string | null>(null)
  const [addingNatureSoundIds, setAddingNatureSoundIds] = useState<Set<string>>(new Set())
  const [isFiltering, setIsFiltering] = useState(false)
  const [isAddingAllPlaying, setIsAddingAllPlaying] = useState(false)

  // Use the natural sound players store
  const { players, createPlayer, togglePlay, setVolume, toggleMute, removePlayer, cleanup } = useNatureSoundPlayers()

  const { data: allNatureSounds = [], isLoading } = useGetNatureSounds({ isPublic: true })
  const { data: currentPlaylist, isLoading: isLoadingPlaylist } = useGetMusicPlaylistUser(playlistId)
  const addNatureSoundsToPlaylist = useAddNatureSoundsToMusicPlaylistUser()

  // Get IDs of natural sounds already in the playlist
  const existingNatureSoundIds = useMemo(() => {
    return currentPlaylist?.natureSounds?.map(sound => sound.id) || []
  }, [currentPlaylist?.natureSounds])

  // Get currently playing natural sounds
  const currentlyPlayingSounds = useMemo(() => {
    return Object.values(players).filter(player => player.isPlaying)
  }, [players])

  // Get currently playing sound IDs that are not already in the playlist and not already being added
  const playingSoundsToAdd = useMemo(() => {
    return currentlyPlayingSounds
      .map(player => player.id)
      .filter(id =>
        !existingNatureSoundIds.includes(id) &&
        !addedNatureSoundIds.has(id) &&
        !addingNatureSoundIds.has(id)
      )
  }, [currentlyPlayingSounds, existingNatureSoundIds, addedNatureSoundIds, addingNatureSoundIds])

  // Get all unique categories from available natural sounds (excluding already added)
  const allCategories = useMemo(() => {
    const categorySet = new Set<string>()
    allNatureSounds.forEach(sound => {
      // Only include categories from sounds not already in the playlist
      if (!existingNatureSoundIds.includes(sound.id) && sound.category) {
        sound.category.forEach(cat => categorySet.add(cat))
      }
    })
    return Array.from(categorySet).sort()
  }, [allNatureSounds, existingNatureSoundIds])

  // Filter natural sounds based on search, category, and exclude already added sounds
  const filteredNatureSounds = useMemo(() => {
    return allNatureSounds.filter(sound => {
      // Exclude sounds already in the playlist
      const notInPlaylist = !existingNatureSoundIds.includes(sound.id)

      const matchesSearch = searchQuery === "" ||
        sound.title.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesCategory = selectedCategories.length === 0 ||
        (sound.category && selectedCategories.every(category => sound.category?.includes(category as any)))

      return notInPlaylist && matchesSearch && matchesCategory
    })
  }, [allNatureSounds, existingNatureSoundIds, searchQuery, selectedCategories])

  const handleCategoryToggle = (category: string) => {
    setIsFiltering(true)
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
    // Brief loading state for smooth UX
    setTimeout(() => setIsFiltering(false), 150)
  }

  const handleAddNatureSound = async (natureSoundId: string) => {
    // Prevent multiple simultaneous requests for the same sound
    if (addingNatureSoundIds.has(natureSoundId) || addedNatureSoundIds.has(natureSoundId)) {
      return
    }

    setAddingNatureSoundIds(prev => new Set(prev).add(natureSoundId))

    try {
      await addNatureSoundsToPlaylist.mutateAsync({
        musicPlaylistUserId: playlistId,
        natureSoundIds: [natureSoundId]
      })
      setAddedNatureSoundIds(prev => new Set(prev).add(natureSoundId))
      toast.success("Natural sound added to playlist")
    } catch (error) {
      console.error(error)
      toast.error("Failed to add natural sound")
    } finally {
      setAddingNatureSoundIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(natureSoundId)
        return newSet
      })
    }
  }

  const handleAddAllPlaying = async () => {
    if (playingSoundsToAdd.length === 0 || isAddingAllPlaying) {
      return
    }

    setIsAddingAllPlaying(true)

    // Mark all playing sounds as being added to prevent individual adds
    setAddingNatureSoundIds(prev => {
      const newSet = new Set(prev)
      playingSoundsToAdd.forEach(id => newSet.add(id))
      return newSet
    })

    try {
      await addNatureSoundsToPlaylist.mutateAsync({
        musicPlaylistUserId: playlistId,
        natureSoundIds: playingSoundsToAdd
      })

      // Mark all as successfully added
      setAddedNatureSoundIds(prev => {
        const newSet = new Set(prev)
        playingSoundsToAdd.forEach(id => newSet.add(id))
        return newSet
      })

      toast.success(`${playingSoundsToAdd.length} natural sound${playingSoundsToAdd.length !== 1 ? 's' : ''} added to playlist`)
    } catch (error) {
      console.error(error)
      toast.error("Failed to add playing natural sounds")
    } finally {
      // Remove from adding state
      setAddingNatureSoundIds(prev => {
        const newSet = new Set(prev)
        playingSoundsToAdd.forEach(id => newSet.delete(id))
        return newSet
      })
      setIsAddingAllPlaying(false)
    }
  }

  const handlePlayNatureSound = (sound: any) => {
    const soundSrc = sound.src || `https://example.com/nature-sounds/${sound.id}.mp3`

    // Create player if it doesn't exist
    if (!players[sound.id]) {
      createPlayer(sound.id, soundSrc)
    }

    // Toggle play/pause
    togglePlay(sound.id)
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedCategories([])
  }

  // Cleanup when dialog closes
  useEffect(() => {
    if (!isOpen) {
      cleanup()
    }
  }, [isOpen, cleanup])

  const getCategoryColor = (category: string, isSelected: boolean = false) => {
    if (isSelected) {
      // Subtle emerald tones for selected categories
      return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/40 dark:text-emerald-300 border-emerald-300 dark:border-emerald-700'
    }

    const colors = {
      'RAIN': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      'FOREST': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800',
      'OCEAN': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300 border-cyan-200 dark:border-cyan-800',
      'THUNDER': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800',
      'WIND': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800',
      'FIRE': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 border-orange-200 dark:border-orange-800',
      'BIRDS': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
      'WATERFALL': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      'STREAM': 'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 border-teal-200 dark:border-teal-800',
      'WAVES': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800',
      'NIGHT': 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300 border-slate-200 dark:border-slate-800',
      'INSECTS': 'bg-lime-100 text-lime-800 dark:bg-lime-900/30 dark:text-lime-300 border-lime-200 dark:border-lime-800',
      'RIVER': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      'STORM': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800',
      'LAKE': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      'WHITE_NOISE': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800',
      'AMBIENT': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800',
      'CITY': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 border-orange-200 dark:border-orange-800',
      'PEOPLE': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300 border-pink-200 dark:border-pink-800'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="!max-w-[50vw] !w-[50vw] !h-[85vh] flex flex-col p-0 sm:!max-w-[50vw] md:!max-w-[50vw] lg:!max-w-[50vw] xl:!max-w-[50vw] 2xl:!max-w-[50vw]" style={{ maxWidth: '50vw', width: '50vw', height: '85vh' }}>
        <DialogHeader className="p-6 pb-4 border-b border-border/50">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30">
              <Plus className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-xl font-semibold">Add Natural Sounds to Playlist</DialogTitle>
              <DialogDescription className="mt-1">
                Add nature sounds to <span className="font-medium text-foreground">"{playlistName}"</span>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0">
          {/* Search and Filter Section */}
          <div className="p-4 space-y-3 border-b border-border/50 bg-muted/20">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search natural sounds by title..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-10 text-sm bg-background focus:ring-2 focus:ring-emerald-500/20"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchQuery("")}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Category Filters */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Filter className="h-3.5 w-3.5 text-muted-foreground" />
                  <span className="text-sm font-medium">Categories</span>
                  {selectedCategories.length > 1 && (
                    <span className="text-xs text-muted-foreground ml-1">(must have all)</span>
                  )}
                  {selectedCategories.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      {selectedCategories.length}
                    </Badge>
                  )}
                </div>
                {(searchQuery || selectedCategories.length > 0) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="h-7 px-2 text-xs"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Clear
                  </Button>
                )}
              </div>

              <div className="flex flex-wrap gap-1.5">
                {allCategories.map(category => {
                  const isSelected = selectedCategories.includes(category)
                  return (
                    <Badge
                      key={category}
                      variant={isSelected ? "default" : "outline"}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:scale-105 text-xs px-2 py-1 h-6",
                        isSelected
                          ? getCategoryColor(category, true)
                          : "hover:bg-emerald-50 border-muted-foreground/20 hover:border-emerald-200 hover:text-emerald-700 dark:hover:bg-emerald-950/20 dark:hover:text-emerald-400 dark:hover:border-emerald-800"
                      )}
                      onClick={() => handleCategoryToggle(category)}
                    >
                      {categoryDisplayNames[category] || category}
                    </Badge>
                  )
                })}
              </div>
            </div>

            {/* Results Summary */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pt-1">
              <span>
                <strong className="text-foreground">{filteredNatureSounds.length}</strong> sound{filteredNatureSounds.length !== 1 ? 's' : ''} available
                {existingNatureSoundIds.length > 0 && (
                  <span className="ml-2 text-emerald-600 dark:text-emerald-400">
                    ({existingNatureSoundIds.length} already in playlist)
                  </span>
                )}
                {currentlyPlayingSounds.length > 0 && (
                  <span className="ml-2 text-blue-600 dark:text-blue-400">
                    ({currentlyPlayingSounds.length} playing)
                  </span>
                )}
              </span>

              <div className="flex items-center gap-3">
                {/* Add Playing Button */}
                {playingSoundsToAdd.length > 0 && (
                  <Button
                    onClick={handleAddAllPlaying}
                    disabled={isAddingAllPlaying}
                    size="sm"
                    className="h-6 px-2 text-xs bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white"
                  >
                    {isAddingAllPlaying ? (
                      <>
                        <Loader2 className="h-2.5 w-2.5 mr-1 animate-spin" />
                        Adding...
                      </>
                    ) : (
                      <>
                        <Plus className="h-2.5 w-2.5 mr-1" />
                        Add Playing ({playingSoundsToAdd.length})
                      </>
                    )}
                  </Button>
                )}

                {/* Added Count */}
                {addedNatureSoundIds.size > 0 && (
                  <span className="text-green-600 dark:text-green-400 font-medium">
                    <strong>{addedNatureSoundIds.size}</strong> added
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Natural Sounds List */}
          <ScrollArea className="flex-1 overflow-y-auto">
            <div className="p-4">
              {isLoading || isLoadingPlaylist || isFiltering ? (
                <div className="space-y-2">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: i * 0.05 }}
                      className="flex items-center gap-3 p-3 rounded-lg border border-border/50 bg-muted/10"
                    >
                      <Skeleton className="h-10 w-10 rounded-lg animate-pulse" />
                      <div className="flex-1 space-y-1.5">
                        <Skeleton className="h-3.5 w-3/4 animate-pulse" />
                        <div className="flex gap-2">
                          <Skeleton className="h-2.5 w-16 animate-pulse" />
                          <Skeleton className="h-2.5 w-20 animate-pulse" />
                        </div>
                      </div>
                      <div className="flex gap-1.5">
                        <Skeleton className="h-7 w-7 rounded animate-pulse" />
                        <Skeleton className="h-6 w-12 rounded animate-pulse" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : filteredNatureSounds.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-center py-12 space-y-3"
                >
                  <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 flex items-center justify-center">
                    <Waves className="h-6 w-6 text-emerald-500/70" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-base font-semibold text-muted-foreground">No natural sounds found</h3>
                    <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                      {searchQuery || selectedCategories.length > 0
                        ? "Try adjusting your search or filter criteria"
                        : existingNatureSoundIds.length > 0
                        ? "All available natural sounds are already in this playlist"
                        : "No natural sounds are available to add"
                      }
                    </p>
                  </div>
                  {(searchQuery || selectedCategories.length > 0) && (
                    <Button variant="outline" size="sm" onClick={clearFilters} className="hover:bg-emerald-50 hover:text-emerald-700 hover:border-emerald-200 dark:hover:bg-emerald-950/20 dark:hover:text-emerald-400 dark:hover:border-emerald-800">
                      <X className="h-3 w-3 mr-1" />
                      Clear Filters
                    </Button>
                  )}
                </motion.div>
              ) : (
                <div className="space-y-0.5">
                  <AnimatePresence>
                    {filteredNatureSounds.map((sound, index) => {
                      const isAdded = addedNatureSoundIds.has(sound.id)
                      const isHovered = hoveredNatureSoundId === sound.id
                      const player = players[sound.id]
                      const isCurrentlyPlaying = player?.isPlaying || false
                      const isAddingThis = addingNatureSoundIds.has(sound.id)

                      return (
                        <motion.div
                          key={sound.id}
                          layout
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -5 }}
                          transition={{ duration: 0.1, delay: index * 0.01 }}
                          className={cn(
                            "group flex items-center gap-3 px-3 py-2 rounded-md border transition-all duration-200",
                            isAdded && "bg-green-50/50 dark:bg-green-950/20 border-green-200 dark:border-green-800",
                            isCurrentlyPlaying && "bg-emerald-50/50 dark:bg-emerald-950/20 border-emerald-200 dark:border-emerald-800",
                            !isAdded && !isCurrentlyPlaying && "border-border/30 hover:border-border hover:bg-muted/20"
                          )}
                          onMouseEnter={() => setHoveredNatureSoundId(sound.id)}
                          onMouseLeave={() => setHoveredNatureSoundId(null)}
                        >
                          {/* Sound Icon */}
                          <div className="w-7 h-7 rounded-md bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/40 dark:to-teal-900/40 flex items-center justify-center shrink-0">
                            <Waves className="h-3.5 w-3.5 text-emerald-600 dark:text-emerald-400" />
                          </div>

                          {/* Sound Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5">
                              <h4 className="font-medium text-sm truncate">
                                {sound.title}
                              </h4>
                            </div>
                            <div className="flex items-center gap-1.5 text-xs text-muted-foreground mt-0.5">
                              <span>∞</span> {/* Duration placeholder for looping sounds */}
                              {sound.category && sound.category.length > 0 && (
                                <>
                                  <span>•</span>
                                  <span className="truncate">{categoryDisplayNames[sound.category[0]] || sound.category[0]}</span>
                                </>
                              )}
                            </div>
                          </div>

                          {/* Audio Controls */}
                          <div className="flex items-center gap-2 shrink-0">
                            {/* Volume Controls - Show when playing or hovered */}
                            {(isCurrentlyPlaying || isHovered) && (
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    if (player) {
                                      toggleMute(sound.id)
                                    }
                                  }}
                                  className="w-6 h-6"
                                >
                                  {player?.isMuted ? (
                                    <VolumeX className="h-3 w-3" />
                                  ) : (
                                    <Volume2 className="h-3 w-3" />
                                  )}
                                </Button>
                                <Slider
                                  value={[player?.isMuted ? 0 : (player?.volume || 80)]}
                                  min={0}
                                  max={100}
                                  step={1}
                                  onValueChange={(value) => {
                                    setVolume(sound.id, value[0])
                                  }}
                                  className="w-16"
                                />
                              </div>
                            )}

                            {/* Play/Pause Button */}
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation()
                                handlePlayNatureSound(sound)
                              }}
                              className={cn(
                                "w-7 h-7 transition-all hover:bg-muted",
                                isCurrentlyPlaying && "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400"
                              )}
                            >
                              {isCurrentlyPlaying ? (
                                <Pause className="h-3.5 w-3.5" />
                              ) : (
                                <Play className="h-3.5 w-3.5" />
                              )}
                            </Button>

                            {/* Add Button */}
                            <Button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleAddNatureSound(sound.id)
                              }}
                              disabled={isAdded || isAddingThis}
                              size="sm"
                              className={cn(
                                "h-6 px-2.5 text-xs transition-all duration-200",
                                isAdded
                                  ? "bg-green-500 hover:bg-green-600 text-white"
                                  : isAddingThis
                                  ? "bg-gray-400 text-white cursor-not-allowed"
                                  : "bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white"
                              )}
                            >
                              {isAdded ? (
                                <>
                                  <Check className="h-2.5 w-2.5 mr-1" />
                                  Added
                                </>
                              ) : isAddingThis ? (
                                <>
                                  <div className="h-2.5 w-2.5 mr-1 border border-white border-t-transparent rounded-full animate-spin" />
                                  Adding...
                                </>
                              ) : (
                                <>
                                  <Plus className="h-2.5 w-2.5 mr-1" />
                                  Add
                                </>
                              )}
                            </Button>
                          </div>
                        </motion.div>
                      )
                    })}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  )
}
